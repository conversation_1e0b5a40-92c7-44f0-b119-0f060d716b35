<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>پیشنهاد توسعه نرم‌افزار حسابداری صرافی</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .proposal-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 20px 0;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .intro {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .intro h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .pricing-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .pricing-item {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .pricing-item h4 {
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .price {
            font-size: 2rem;
            font-weight: bold;
            color: #f39c12;
            margin: 10px 0;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }

        .tech-item {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
        }

        .timeline {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .timeline-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 20px;
        }

        .contact-section {
            background: #2c3e50;
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .contact-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }

        .highlight {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .guarantee {
            background: #27ae60;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="proposal-card">
            <div class="header">
                <h1>🏦 پیشنهاد توسعه نرم‌افزار حسابداری صرافی</h1>
                <p>راه‌حل جامع و حرفه‌ای برای مدیریت عملیات صرافی</p>
            </div>

            <div class="content">
                <div class="intro">
                    <h3>👋 درباره من</h3>
                    <p>سلام و احترام، من امیرحسین دادبین هستم، یک توسعه‌دهنده فول‌استک با بیش از 5 سال تجربه در زمینه توسعه نرم‌افزارهای مالی و حسابداری. تخصص من در ایجاد سیستم‌های پیچیده مالی، ERP و نرم‌افزارهای حسابداری است.</p>
                </div>

                <div class="section">
                    <h2>🎯 درک پروژه</h2>
                    <p>با توجه به نیازهای شما، یک نرم‌افزار حسابداری تحت وب برای صرافی طراحی خواهم کرد که شامل تمامی امکانات مورد نیاز شما باشد:</p>
                    
                    <div class="features-grid">
                        <div class="feature-card">
                            <h4>👥 مدیریت مشتریان</h4>
                            <p>ثبت کامل مشخصات مشتریان، تاریخچه معاملات و یادداشت‌های مربوطه</p>
                        </div>
                        <div class="feature-card">
                            <h4>💱 مدیریت معاملات</h4>
                            <p>ثبت دقیق خرید و فروش ارز با تمام جزئیات و محاسبه خودکار سود</p>
                        </div>
                        <div class="feature-card">
                            <h4>🏢 چند لوکیشن</h4>
                            <p>مدیریت دفاتر مختلف و موجودی ارز در هر مکان</p>
                        </div>
                        <div class="feature-card">
                            <h4>📊 گزارش‌گیری پیشرفته</h4>
                            <p>تولید گزارش‌های دقیق با خروجی PDF و Excel</p>
                        </div>
                        <div class="feature-card">
                            <h4>🔐 مدیریت کاربران</h4>
                            <p>سطوح دسترسی مختلف و امنیت بالا</p>
                        </div>
                        <div class="feature-card">
                            <h4>📱 طراحی ریسپانسیو</h4>
                            <p>قابل استفاده روی تمام دستگاه‌ها</p>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>💻 تکنولوژی پیشنهادی</h2>
                    <p>با توجه به نیازهای پروژه، دو گزینه تکنولوژی پیشنهاد می‌دهم:</p>
                    
                    <div class="tech-stack">
                        <div class="tech-item">Python + Django</div>
                        <div class="tech-item">JavaScript + Node.js</div>
                        <div class="tech-item">PostgreSQL</div>
                        <div class="tech-item">HTML5 + CSS3</div>
                        <div class="tech-item">Bootstrap</div>
                        <div class="tech-item">Chart.js</div>
                    </div>
                    
                    <div class="highlight">
                        <h4>🚀 چرا این تکنولوژی‌ها؟</h4>
                        <p>سبک، سریع، قابل اعتماد و قابل نصب روی سرورهای ویندوزی</p>
                    </div>
                </div>

                <div class="pricing-section">
                    <h2>💰 پیشنهاد قیمت - 12,000,000 تومان</h2>
                    <div class="pricing-grid">
                        <div class="pricing-item">
                            <h4>🔧 آماده‌سازی بک‌اند + تست</h4>
                            <div class="price">5,000,000 تومان</div>
                            <p>توسعه API، دیتابیس و منطق کسب‌وکار</p>
                        </div>
                        <div class="pricing-item">
                            <h4>🎨 آماده‌سازی فرانت‌اند + تست</h4>
                            <div class="price">3,000,000 تومان</div>
                            <p>طراحی رابط کاربری و تجربه کاربری</p>
                        </div>
                        <div class="pricing-item">
                            <h4>🚀 تست و دیپلوی نهایی</h4>
                            <div class="price">3,000,000 تومان</div>
                            <p>تست کامل سیستم و راه‌اندازی</p>
                        </div>
                        <div class="pricing-item">
                            <h4>🖥️ خرید سرور (تخمینی)</h4>
                            <div class="price">1,000,000 تومان</div>
                            <p>هزینه سرور و راه‌اندازی</p>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>⏰ زمان‌بندی پروژه - 10 روز</h2>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-number">1</div>
                            <div>
                                <h4>روزهای 1-3: طراحی دیتابیس و بک‌اند</h4>
                                <p>ایجاد ساختار دیتابیس، API ها و منطق کسب‌وکار</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-number">2</div>
                            <div>
                                <h4>روزهای 4-6: توسعه فرانت‌اند</h4>
                                <p>طراحی رابط کاربری و اتصال به بک‌اند</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-number">3</div>
                            <div>
                                <h4>روزهای 7-8: تست و بهینه‌سازی</h4>
                                <p>تست کامل عملکرد و رفع باگ‌ها</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-number">4</div>
                            <div>
                                <h4>روزهای 9-10: دیپلوی و تحویل</h4>
                                <p>راه‌اندازی روی سرور و آموزش کاربری</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="guarantee">
                    <h3>✅ تضمین‌های من</h3>
                    <p>✓ تحویل کامل سورس‌کد و دیتابیس<br>
                    ✓ مستندات کامل پروژه<br>
                    ✓ 30 روز پشتیبانی رایگان<br>
                    ✓ آموزش کامل نحوه استفاده</p>
                </div>

                <div class="contact-section">
                    <h2>📞 اطلاعات تماس</h2>
                    <div class="contact-info">
                        <div class="contact-item">
                            <h4>👤 نام</h4>
                            <p>امیرحسین دادبین</p>
                        </div>
                        <div class="contact-item">
                            <h4>💼 تخصص</h4>
                            <p>توسعه‌دهنده فول‌استک</p>
                        </div>
                        <div class="contact-item">
                            <h4>🏆 تجربه</h4>
                            <p>5+ سال در پروژه‌های مالی</p>
                        </div>
                        <div class="contact-item">
                            <h4>🌐 پلتفرم</h4>
                            <p>پونیشا فریلنسر</p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <a href="#" class="btn">💬 شروع همکاری</a>
                        <a href="#" class="btn">📋 مشاهده نمونه‌کارها</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
